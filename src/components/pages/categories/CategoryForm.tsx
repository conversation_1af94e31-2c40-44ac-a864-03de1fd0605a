import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "@/components/custom-ui/toast";
import { useBusinessContext } from "@/context/BusinessContext";
import { useUserContext } from "@/context/UserContext";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import type { ICategory, ICategoryCreatePayload } from "@/types";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export const categoryFormSchema = z.object({
	id: z.number().optional(),
	name: z.string().min(1, "Name is required"),
})

interface CategoryFormProps {
	initialData?: ICategory;
	onCancel: () => void;
}

export function CategoryForm({ initialData, onCancel }: CategoryFormProps) {
	const { user } = useUserContext();
	const {
		entityActions: {
			createEntity
		},
		fetchActions: {
			fetchCategories
		}
	} = useBusinessContext()
	const { addToast } = useToast();

	const form = useForm<z.infer<typeof categoryFormSchema>>({
		resolver: zodResolver(categoryFormSchema),
		defaultValues: {
			id: initialData?.id || 0,
			name: initialData?.name || "",
		}
	})

	const handleSubmit = async (data: z.infer<typeof categoryFormSchema>) => {
		if (!user) return addToast({
			type: "error",
			title: "Category creation failed",
			description: "You must be logged in to create a category."
		});

		const payload: ICategoryCreatePayload = {
			name: data.name
		}

		const response = await createEntity<ICategoryCreatePayload, ICategory>("/v1/categories", payload)
		if (response.error) return addToast({
			type: "error",
			title: "Category creation failed",
			description: response.error || "Failed to create category"
		})
		addToast({
			type: "success",
			title: "Category created",
			description: "The category has been created successfully."
		})
		fetchCategories()
		onCancel();
	}

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
				<FormField
					control={form.control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Name</FormLabel>
							<FormControl>
								<Input placeholder="Enter name" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="flex justify-end gap-2 pt-4">
					<Button
						type="button"
						variant="outline"
						onClick={onCancel}
					>
						Cancel
					</Button>
					<Button
						type="submit"
					>
						Save
					</Button>
				</div>
			</form>
		</Form>
	)
}